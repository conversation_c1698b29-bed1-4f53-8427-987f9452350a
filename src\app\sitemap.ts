import { MetadataRoute } from 'next'
import { getBlogPosts, BlogPost } from '@/lib/blog'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Use environment variable or default to localhost for development
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.VERCEL_URL
    ? `https://${process.env.VERCEL_URL}`
    : `http://localhost:${process.env.PORT || 3000}`
  const currentDate = new Date()

  // Get all blog posts for all languages
  const enPosts = await getBlogPosts('en')
  const frPosts = await getBlogPosts('fr')
  const arPosts = await getBlogPosts('ar')
  
  // Static pages with enhanced priority and frequency settings
  const staticPages = [
    // Root redirect page (redirects to default language)
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily' as const,
      priority: 1.0, // Highest priority for root page
    },
    {
      url: `${baseUrl}/en`,
      lastModified: currentDate,
      changeFrequency: 'daily' as const,
      priority: 1.0, // Highest priority for main calculator page
    },
    {
      url: `${baseUrl}/fr`,
      lastModified: currentDate,
      changeFrequency: 'daily' as const,
      priority: 1.0, // Highest priority for main calculator page
    },
    {
      url: `${baseUrl}/ar`,
      lastModified: currentDate,
      changeFrequency: 'daily' as const,
      priority: 1.0, // Highest priority for main calculator page
    },
    {
      url: `${baseUrl}/en/blog`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.8, // High priority for blog listing
    },
    {
      url: `${baseUrl}/fr/blog`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.8, // High priority for blog listing
    },
    {
      url: `${baseUrl}/ar/blog`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.8, // High priority for blog listing
    },
    {
      url: `${baseUrl}/en/faq`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.9, // Very high priority for FAQ (targets informational queries)
    },
    {
      url: `${baseUrl}/fr/faq`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.9, // Very high priority for FAQ (targets informational queries)
    },
    {
      url: `${baseUrl}/ar/faq`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.9, // Very high priority for FAQ (targets informational queries)
    },
  ]
  
  // Blog post pages with dynamic priority based on featured status and recency
  const blogPages = [
    ...enPosts.map((post: BlogPost) => {
      const publishDate = new Date(post.publishedAt);
      const daysSincePublished = Math.floor((currentDate.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24));

      // Calculate priority based on featured status and recency
      let priority = 0.6; // Base priority
      if (post.featured) priority += 0.2; // Featured posts get higher priority
      if (daysSincePublished < 30) priority += 0.1; // Recent posts get boost
      if (daysSincePublished < 7) priority += 0.1; // Very recent posts get additional boost

      return {
        url: `${baseUrl}/en/blog/${post.slug}`,
        lastModified: publishDate,
        changeFrequency: daysSincePublished < 30 ? 'weekly' as const : 'monthly' as const,
        priority: Math.min(priority, 0.9), // Cap at 0.9
      };
    }),
    ...frPosts.map((post: BlogPost) => {
      const publishDate = new Date(post.publishedAt);
      const daysSincePublished = Math.floor((currentDate.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24));

      // Calculate priority based on featured status and recency
      let priority = 0.6; // Base priority
      if (post.featured) priority += 0.2; // Featured posts get higher priority
      if (daysSincePublished < 30) priority += 0.1; // Recent posts get boost
      if (daysSincePublished < 7) priority += 0.1; // Very recent posts get additional boost

      return {
        url: `${baseUrl}/fr/blog/${post.slug}`,
        lastModified: publishDate,
        changeFrequency: daysSincePublished < 30 ? 'weekly' as const : 'monthly' as const,
        priority: Math.min(priority, 0.9), // Cap at 0.9
      };
    }),
    ...arPosts.map((post: BlogPost) => {
      const publishDate = new Date(post.publishedAt);
      const daysSincePublished = Math.floor((currentDate.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24));

      // Calculate priority based on featured status and recency
      let priority = 0.6; // Base priority
      if (post.featured) priority += 0.2; // Featured posts get higher priority
      if (daysSincePublished < 30) priority += 0.1; // Recent posts get boost
      if (daysSincePublished < 7) priority += 0.1; // Very recent posts get additional boost

      return {
        url: `${baseUrl}/ar/blog/${post.slug}`,
        lastModified: publishDate,
        changeFrequency: daysSincePublished < 30 ? 'weekly' as const : 'monthly' as const,
        priority: Math.min(priority, 0.9), // Cap at 0.9
      };
    }),
  ]
  
  // Combine all pages and sort by priority (highest first) for better SEO
  const allPages = [...staticPages, ...blogPages].sort((a, b) => (b.priority || 0) - (a.priority || 0))

  return allPages
}
