import { NextResponse } from 'next/server'

export async function GET() {
  // Use environment variable or default to localhost for development
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.VERCEL_URL
    ? `https://${process.env.VERCEL_URL}`
    : `http://localhost:${process.env.PORT || 3000}`

  const currentDate = new Date().toISOString()

  // Static pages sitemap
  const staticPages = [
    // Root page
    { url: baseUrl, priority: '1.0', changefreq: 'daily' },
    // Main calculator pages
    { url: `${baseUrl}/en`, priority: '1.0', changefreq: 'daily' },
    { url: `${baseUrl}/fr`, priority: '1.0', changefreq: 'daily' },
    { url: `${baseUrl}/ar`, priority: '1.0', changefreq: 'daily' },
    // FAQ pages
    { url: `${baseUrl}/en/faq`, priority: '0.9', changefreq: 'monthly' },
    { url: `${baseUrl}/fr/faq`, priority: '0.9', changefreq: 'monthly' },
    { url: `${baseUrl}/ar/faq`, priority: '0.9', changefreq: 'monthly' },
    // Blog listing pages
    { url: `${baseUrl}/en/blog`, priority: '0.8', changefreq: 'weekly' },
    { url: `${baseUrl}/fr/blog`, priority: '0.8', changefreq: 'weekly' },
    { url: `${baseUrl}/ar/blog`, priority: '0.8', changefreq: 'weekly' },
  ]

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${staticPages.map(page => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
    ${page.url.includes('/en') ? `<xhtml:link rel="alternate" hreflang="en" href="${page.url.replace('/en', '/en')}" />
    <xhtml:link rel="alternate" hreflang="fr" href="${page.url.replace('/en', '/fr')}" />
    <xhtml:link rel="alternate" hreflang="ar" href="${page.url.replace('/en', '/ar')}" />` : ''}
    ${page.url.includes('/fr') ? `<xhtml:link rel="alternate" hreflang="en" href="${page.url.replace('/fr', '/en')}" />
    <xhtml:link rel="alternate" hreflang="fr" href="${page.url.replace('/fr', '/fr')}" />
    <xhtml:link rel="alternate" hreflang="ar" href="${page.url.replace('/fr', '/ar')}" />` : ''}
    ${page.url.includes('/ar') ? `<xhtml:link rel="alternate" hreflang="en" href="${page.url.replace('/ar', '/en')}" />
    <xhtml:link rel="alternate" hreflang="fr" href="${page.url.replace('/ar', '/fr')}" />
    <xhtml:link rel="alternate" hreflang="ar" href="${page.url.replace('/ar', '/ar')}" />` : ''}
  </url>`).join('\n')}
</urlset>`

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  })
}
